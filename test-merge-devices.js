/**
 * 测试重构后的 mergeDevices 方法
 * 这个文件用于验证重构后的逻辑是否符合要求
 */

// 模拟测试数据
const historyDevices = [
  // 有线设备
  {
    id: 'ABC123',
    serialNo: 'ABC123',
    wifi: false,
    name: '有线设备1',
    status: 'offline',
    remark: '历史备注1'
  },
  
  // 无线设备 - 同一设备的不同连接
  {
    id: '192.168.1.100:5555',
    serialNo: 'DEF456',
    wifi: true,
    name: '无线设备1',
    status: 'offline',
    remark: '历史备注2'
  },
  {
    id: '192.168.1.100:5556',
    serialNo: 'DEF456',
    wifi: true,
    name: '无线设备1',
    status: 'offline',
    remark: '历史备注3'
  },
  
  // 无线设备 - 另一个设备
  {
    id: '192.168.1.101:5555',
    serialNo: 'GHI789',
    wifi: true,
    name: '无线设备2',
    status: 'offline',
    remark: '历史备注4'
  }
]

const currentDevices = [
  // 有线设备
  {
    id: 'ABC123',
    serialNo: 'ABC123',
    wifi: false,
    name: '有线设备1',
    status: 'device'
  },
  
  // 无线设备 - 当前连接
  {
    id: '192.168.1.100:5555',
    serialNo: 'DEF456',
    wifi: true,
    name: '无线设备1',
    status: 'device'
  },
  {
    id: '192.168.1.100:5557', // 新的端口连接
    serialNo: 'DEF456',
    wifi: true,
    name: '无线设备1',
    status: 'device'
  }
]

console.log('=== 测试数据 ===')
console.log('历史设备:', historyDevices)
console.log('当前设备:', currentDevices)

console.log('\n=== 预期行为 ===')
console.log('1. 有线设备 ABC123 应该合并历史信息（备注等）')
console.log('2. 无线设备 192.168.1.100:5555 应该保持历史备注，但不合并其他历史设备的配置')
console.log('3. 无线设备 192.168.1.100:5557 应该合并来自 192.168.1.100:5556 的历史配置')
console.log('4. 历史设备 192.168.1.101:5555 应该保留在结果中（未被当前设备匹配）')

// 注意：这个测试文件只是用于说明预期行为
// 实际测试需要在真实环境中运行，因为依赖了 window.appStore 等全局对象
